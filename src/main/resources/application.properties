spring.application.name=money-kids-back

spring.datasource.url=${DB_URL:***************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=none

spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Flyway ë¹íì±í
spring.flyway.enabled=false

spring.security.user.name=${SECURITY_USER_NAME:admin}
spring.security.user.password=${SECURITY_USER_PASSWORD:admin}
logging.level.org.springframework.security=DEBUG

# ====== OAuth2 Google ì¤ì  ======
spring.security.oauth2.client.registration.google.client-id=${GOOGLE_CLIENT_ID:your-google-client-id}
spring.security.oauth2.client.registration.google.client-secret=${GOOGLE_CLIENT_SECRET:your-google-client-secret}
spring.security.oauth2.client.registration.google.scope=openid,profile,email
spring.security.oauth2.client.registration.google.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.google.redirect-uri=http://localhost:8080/login/oauth2/code/google

# OAuth2 provider URLs
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v2/userinfo
spring.security.oauth2.client.provider.google.user-name-attribute=id

spring.security.oauth2.client.registration.kakao.client-id=${KAKAO_CLIENT_ID:your-kakao-client-id}
spring.security.oauth2.client.registration.kakao.client-secret=${KAKAO_CLIENT_SECRET:your-kakao-client-secret}
spring.security.oauth2.client.registration.kakao.redirect-uri=http://localhost:8080/api/users/login/kakao/callback
spring.security.oauth2.client.registration.kakao.client-authentication-method=POST
spring.security.oauth2.client.registration.kakao.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.kakao.scope=profile_nickname,account_email
spring.security.oauth2.client.provider.kakao.authorization-uri=https://kauth.kakao.com/oauth/authorize
spring.security.oauth2.client.provider.kakao.token-uri=https://kauth.kakao.com/oauth/token
spring.security.oauth2.client.provider.kakao.user-info-uri=https://kapi.kakao.com/v2/user/me
spring.security.oauth2.client.provider.kakao.user-name-attribute=id

# ====== OpenAI API ì¤ì  ======
openai.api.key=${OPENAI_API_KEY:your-openai-api-key-here}
openai.api.url=${OPENAI_API_URL:https://api.openai.com/v1/chat/completions}

# ====== .env íì¼ ë¡ë ì¤ì  ======
spring.config.import=optional:dotenv:.env