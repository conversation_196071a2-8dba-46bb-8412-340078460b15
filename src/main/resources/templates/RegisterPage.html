<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>회원가입</title>
</head>
<body>
<h1>회원가입</h1>
<form action="/api/auth/register" method="post">
    <div>
        <label for="id">아이디:</label>
        <input type="text" id="id" name="id" required>
    </div>
    <div>
        <label for="name">이름:</label>
        <input type="text" id="name" name="name" required>
    </div>
    <div>
        <label for="password">비밀번호:</label>
        <input type="password" id="password" name="password" required>
    </div>
    <button type="submit">가입하기</button>
</form>
<div th:if="${errorMessage}" style="color: red;">
    <p th:text="${errorMessage}"></p>
</div>
<a href="/login">로그인 페이지로</a>
</body>
</html>